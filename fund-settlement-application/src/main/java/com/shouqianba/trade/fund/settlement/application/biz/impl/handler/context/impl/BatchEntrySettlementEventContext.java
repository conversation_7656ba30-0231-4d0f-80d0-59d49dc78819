package com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.impl;

import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.EventStrategyContext;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.holder.EventHandlerContext;
import com.shouqianba.trade.fund.settlement.application.biz.model.FileInfo;
import com.shouqianba.trade.fund.settlement.common.util.JsonUtils;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req.SettlementClearingDetailRequest;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req.SettlementClearingSubmitRequest;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res.SettlementClearingSubmitResult;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.oss.model.OssUploadRequest;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.BatchClearingSettlementStatusEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentBatchEntrySettlementVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.SettlementBatchAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.query.SettlementBatchAggrQuery;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.vo.SettlementBatchBizClearingResultInfoVO;
import lombok.Getter;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/3/17 Time: 16:20 PM
 */
@Getter
public class BatchEntrySettlementEventContext extends EventStrategyContext<EventContentBatchEntrySettlementVO> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.BATCH_ENTRY_SETTLE;


    private EventContentBatchEntrySettlementVO eventContent;
    private SettlementBatchAggrRoot settlementBatchAggrRoot;
    private String bucketName;
    private FileInfo fileInfo;
    private SettlementClearingSubmitResult clearingSubmitResult;


    static {
        registerContext(EVENT_TYPE, new BatchEntrySettlementEventContext());
    }

    private BatchEntrySettlementEventContext() {
    }

    private BatchEntrySettlementEventContext(EventAggrRoot eventAggrRoot) {
        super(eventAggrRoot);
    }

    @Override
    public EventStrategyContext<EventContentBatchEntrySettlementVO> rebuildContext(EventHandlerContext context) {
        return new BatchEntrySettlementEventContext(context.getEventAggrRoot());
    }

    @Override
    public EventContentBatchEntrySettlementVO getBizContent() {
        if (Objects.isNull(eventContent)) {
            eventContent = JsonUtils.parseObject(getEventAggrRoot()
                            .getContent()
                            .getBizContent()
                    , EventContentBatchEntrySettlementVO.class);
        }
        return eventContent;
    }

    public SettlementClearingSubmitRequest genSettlementClearingSubmitRequest(String fileName, String ossFilePath) {
        return SettlementClearingSubmitRequest
                .builder()
                .brandId(settlementBatchAggrRoot
                        .getFromInfo()
                        .getBrandId())      // e.g., "BRAND_001"
                .date(LocalDate
                        .now()
                        .format(DateTimeFormatter.ofPattern("yyyyMMdd")))// todo
                .fundChannel(2) // todo 等永明去掉
                .settlementChannel(settlementBatchAggrRoot
                        .getBizInfo()
                        .getAcquiringCompany()) // todo
                .clientSn(settlementBatchAggrRoot.getIdStr())
                .clearingFile(ossFilePath)
                .bucketName(bucketName)
                .build();
    }


    public SettlementBatchAggrQuery genSettlementBatchAggrQuery() {
        return SettlementBatchAggrQuery
                .builder()
                .id(getEventAggrRoot().getAssociatedSnNum())
                .build();
    }

    public void bindSettlementBatchAggrRoot(SettlementBatchAggrRoot settlementBatchAggrRoot) {
        this.settlementBatchAggrRoot = settlementBatchAggrRoot;
    }

    public SettlementClearingDetailRequest genSettlementClearingDetailRequest() {
        return SettlementClearingDetailRequest
                .builder()
                .clientSn(settlementBatchAggrRoot.getIdStr())
                .build();
    }

    public OssUploadRequest genOssUploadRequest(String fileName, String ossFilePath, byte[] fileContent) {
        return OssUploadRequest
                .builder()
                .bucketName(bucketName)
                .path(ossFilePath)
                .content(fileContent)
                .fileName(fileName)
                .build();
    }

    public void bindBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public void bindFileInfo(FileInfo fileInfo) {
        this.fileInfo = fileInfo;
    }

    public void bindClearingSubmitResult(SettlementClearingSubmitResult clearingSubmitResult) {
        this.clearingSubmitResult = clearingSubmitResult;
    }

    public void updSettlementBatchAggrRootClearingSubmitResult() {
        settlementBatchAggrRoot.updateEntrySettleStatus(BatchClearingSettlementStatusEnum.FAILED);
        settlementBatchAggrRoot.updateClearingResult(SettlementBatchBizClearingResultInfoVO
                .builder()
                .finishTime(clearingSubmitResult
                        .getFinishTimeStr())
                .build());
        settlementBatchAggrRoot.updateClearingSubmitResult(clearingSubmitResult);
    }
}
